/* 全局样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--color-text) !important;
  background-color: var(--color-background) !important;
  transition: color 0.3s ease, background-color 0.3s ease;
}

/* 强制应用主题变量 */
html[data-theme="light"],
body[data-theme="light"] {
  background-color: #ffffff !important;
  color: #2c3e50 !important;
}

html[data-theme="dark"],
body[data-theme="dark"] {
  background-color: #0f1419 !important;
  color: #f0f6fc !important;
}

/* Element Plus 深色主题优化 */
[data-theme="dark"] {
  /* 覆盖 Element Plus 的深色主题变量 */
  --el-bg-color: #1e2328;
  --el-bg-color-page: #0f1419;
  --el-bg-color-overlay: #2a2d32;
  --el-text-color-primary: #f0f6fc;
  --el-text-color-regular: #c9d1d9;
  --el-text-color-secondary: #8b949e;
  --el-text-color-placeholder: #6e7681;
  --el-text-color-disabled: #484f58;
  --el-border-color: rgba(240, 246, 252, 0.08);
  --el-border-color-light: rgba(240, 246, 252, 0.06);
  --el-border-color-lighter: rgba(240, 246, 252, 0.04);
  --el-border-color-extra-light: rgba(240, 246, 252, 0.02);
  --el-border-color-dark: rgba(240, 246, 252, 0.12);
  --el-border-color-darker: rgba(240, 246, 252, 0.16);
  --el-fill-color: rgba(240, 246, 252, 0.04);
  --el-fill-color-light: rgba(240, 246, 252, 0.02);
  --el-fill-color-lighter: rgba(240, 246, 252, 0.01);
  --el-fill-color-extra-light: rgba(240, 246, 252, 0.005);
  --el-fill-color-dark: rgba(240, 246, 252, 0.06);
  --el-fill-color-darker: rgba(240, 246, 252, 0.08);
  --el-fill-color-blank: transparent;
  --el-color-primary: #58a6ff;
  --el-color-primary-light-3: rgba(88, 166, 255, 0.7);
  --el-color-primary-light-5: rgba(88, 166, 255, 0.5);
  --el-color-primary-light-7: rgba(88, 166, 255, 0.3);
  --el-color-primary-light-8: rgba(88, 166, 255, 0.2);
  --el-color-primary-light-9: rgba(88, 166, 255, 0.1);
  --el-color-primary-dark-2: #388bfd;
  --el-color-success: #3fb950;
  --el-color-warning: #d29922;
  --el-color-danger: #f85149;
  --el-color-error: #f85149;
  --el-color-info: #8b949e;
  --el-box-shadow: 0 12px 32px 4px rgba(0, 0, 0, 0.36), 0 8px 20px rgba(0, 0, 0, 0.72);
  --el-box-shadow-light: 0 0 12px rgba(0, 0, 0, 0.36);
  --el-box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.36), 0 0 6px rgba(0, 0, 0, 0.24);
  --el-box-shadow-dark: 0 4px 8px rgba(0, 0, 0, 0.48), 0 0 12px rgba(0, 0, 0, 0.36);
}

/* 深色主题下表格保持浅色样式 */
[data-theme="dark"] .el-table {
  --el-table-bg-color: #ffffff !important;
  --el-table-tr-bg-color: #ffffff !important;
  --el-table-header-bg-color: #fafbfc !important;
  --el-table-row-hover-bg-color: #f6f8fa !important;
  --el-table-current-row-bg-color: #fff5b4 !important;
  --el-table-header-text-color: #24292e !important;
  --el-table-text-color: #24292e !important;
  --el-table-border-color: #e1e4e8 !important;
  --el-table-border: 1px solid #e1e4e8 !important;
  --el-table-expanded-cell-bg-color: #fafbfc !important;

  background-color: #ffffff !important;
  color: #24292e !important;
}

/* 深色主题下表格单元格保持浅色 */
[data-theme="dark"] .el-table td,
[data-theme="dark"] .el-table th {
  background-color: #ffffff !important;
  color: #24292e !important;
  border-color: #e1e4e8 !important;
}

/* 深色主题下表格头部保持浅色 */
[data-theme="dark"] .el-table th.el-table__cell {
  background-color: #fafbfc !important;
  color: #24292e !important;
  border-color: #e1e4e8 !important;
}

/* 深色主题下表格悬停效果保持浅色 */
[data-theme="dark"] .el-table__body tr:hover > td {
  background-color: #f6f8fa !important;
  color: #24292e !important;
}

/* 深色主题下表格选中行保持浅色 */
[data-theme="dark"] .el-table__body tr.current-row > td {
  background-color: #fff5b4 !important;
  color: #24292e !important;
}

/* 深色主题下表格内的按钮保持正常颜色 */
[data-theme="dark"] .el-table .el-button {
  --el-button-text-color: #ffffff;
  --el-button-bg-color: var(--el-color-primary);
  --el-button-border-color: var(--el-color-primary);
}

[data-theme="dark"] .el-table .el-button--primary {
  --el-button-text-color: #ffffff !important;
  --el-button-bg-color: #409eff !important;
  --el-button-border-color: #409eff !important;
}

[data-theme="dark"] .el-table .el-button--success {
  --el-button-text-color: #ffffff !important;
  --el-button-bg-color: #67c23a !important;
  --el-button-border-color: #67c23a !important;
}

[data-theme="dark"] .el-table .el-button--warning {
  --el-button-text-color: #ffffff !important;
  --el-button-bg-color: #e6a23c !important;
  --el-button-border-color: #e6a23c !important;
}

[data-theme="dark"] .el-table .el-button--danger {
  --el-button-text-color: #ffffff !important;
  --el-button-bg-color: #f56c6c !important;
  --el-button-border-color: #f56c6c !important;
}

[data-theme="dark"] .el-table .el-button--info {
  --el-button-text-color: #ffffff !important;
  --el-button-bg-color: #909399 !important;
  --el-button-border-color: #909399 !important;
}

/* 深色主题下表格内的标签保持正常颜色 */
[data-theme="dark"] .el-table .el-tag {
  color: #ffffff !important;
}

[data-theme="dark"] .el-table .el-tag--success {
  background-color: #67c23a !important;
  border-color: #67c23a !important;
  color: #ffffff !important;
}

[data-theme="dark"] .el-table .el-tag--warning {
  background-color: #e6a23c !important;
  border-color: #e6a23c !important;
  color: #ffffff !important;
}

[data-theme="dark"] .el-table .el-tag--danger {
  background-color: #f56c6c !important;
  border-color: #f56c6c !important;
  color: #ffffff !important;
}

[data-theme="dark"] .el-table .el-tag--info {
  background-color: #909399 !important;
  border-color: #909399 !important;
  color: #ffffff !important;
}

/* 深色主题下表格分页器保持浅色 */
[data-theme="dark"] .el-pagination {
  --el-pagination-bg-color: #ffffff !important;
  --el-pagination-text-color: #24292e !important;
  --el-pagination-border-color: #e1e4e8 !important;
  --el-pagination-button-bg-color: #ffffff !important;
  --el-pagination-button-color: #24292e !important;
  --el-pagination-hover-color: #409eff !important;
}

[data-theme="dark"] .el-pagination .el-pager li {
  background-color: #ffffff !important;
  color: #24292e !important;
  border-color: #e1e4e8 !important;
}

[data-theme="dark"] .el-pagination .el-pager li:hover {
  color: #409eff !important;
}

[data-theme="dark"] .el-pagination .el-pager li.is-active {
  background-color: #409eff !important;
  color: #ffffff !important;
}

/* 深色主题下排课表格特殊样式 */
[data-theme="dark"] .schedule-table-container {
  background: #ffffff !important;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

[data-theme="dark"] .schedule-table-container .el-table {
  background-color: #ffffff !important;
}

[data-theme="dark"] .course-item {
  background: #f0f9ff !important;
  border: 1px solid #bae6fd !important;
  color: #0c4a6e !important;
  border-radius: 4px;
  padding: 8px;
  margin: 2px 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

[data-theme="dark"] .course-item:hover {
  background: #e0f2fe !important;
  border-color: #7dd3fc !important;
}

[data-theme="dark"] .course-subject {
  font-weight: 600;
  color: #0c4a6e !important;
  margin-bottom: 4px;
}

[data-theme="dark"] .course-teacher {
  font-size: 12px;
  color: #0369a1 !important;
  margin-bottom: 2px;
}

[data-theme="dark"] .course-class {
  font-size: 12px;
  color: #0284c7 !important;
}

/* 深色主题下所有表格相关容器保持浅色 */
[data-theme="dark"] .el-table-wrapper,
[data-theme="dark"] .el-table__header-wrapper,
[data-theme="dark"] .el-table__body-wrapper,
[data-theme="dark"] .el-table__footer-wrapper {
  background-color: #ffffff !important;
}

/* 深色主题下表格加载状态保持浅色 */
[data-theme="dark"] .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9) !important;
}

[data-theme="dark"] .el-loading-text {
  color: #24292e !important;
}

/* 深色主题下表格空状态保持浅色 */
[data-theme="dark"] .el-table__empty-block {
  background-color: #ffffff !important;
}

[data-theme="dark"] .el-table__empty-text {
  color: #24292e !important;
}

/* 深色主题下表格固定列保持浅色 */
[data-theme="dark"] .el-table__fixed,
[data-theme="dark"] .el-table__fixed-right {
  background-color: #ffffff !important;
}

[data-theme="dark"] .el-table__fixed::before,
[data-theme="dark"] .el-table__fixed-right::before {
  background-color: #e1e4e8 !important;
}

/* 深色主题下表格展开行保持浅色 */
[data-theme="dark"] .el-table__expanded-cell {
  background-color: #fafbfc !important;
  color: #24292e !important;
}

/* 深色主题下表格排序图标保持可见 */
[data-theme="dark"] .el-table th.is-sortable .el-table__sort-caret {
  color: #24292e !important;
}

/* 深色主题下表格筛选图标保持可见 */
[data-theme="dark"] .el-table .el-table__column-filter-trigger {
  color: #24292e !important;
}

/* 确保表格在深色主题下的整体样式 */
[data-theme="dark"] .table-container,
[data-theme="dark"] .schedule-table-container {
  /* 强制覆盖任何可能的深色样式 */
  background: #ffffff !important;
  color: #24292e !important;
}

[data-theme="dark"] .table-container *,
[data-theme="dark"] .schedule-table-container * {
  /* 确保所有子元素都使用浅色主题 */
  color: inherit !important;
}

#app {
  height: 100%;
}

/* 清除默认样式 */
ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  backdrop-filter: blur(10px);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.6), rgba(118, 75, 162, 0.6));
  border-radius: 4px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
  transform: scale(1.1);
}

/* 布局样式 */
.layout {
  display: flex;
  height: 100vh;
}

.layout-sidebar {
  width: 200px;
  background: var(--color-sidebar-background);
  transition: width 0.3s;
}

.layout-sidebar.collapsed {
  width: 64px;
}

.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.layout-header {
  height: 60px;
  background: var(--color-header-background);
  border-bottom: 1px solid var(--color-header-border);
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 1px 4px var(--color-card-shadow);
}

.layout-content {
  flex: 1;
  padding: 20px;
  overflow: auto;
  background: #f5f5f5;
}

/* 卡片样式 */
.card {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  border: 1px solid #e8e8e8;
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  font-weight: 500;
}

.card-body {
  padding: 20px;
}

/* 表格样式 - 深色主题下保持浅色 */
.table-container {
  background: #fff !important;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 深色主题下表格容器保持浅色 */
[data-theme="dark"] .table-container {
  background: #ffffff !important;
  border: 1px solid #e1e4e8 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.table-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafbfc !important;
}

/* 深色主题下表格头部保持浅色 */
[data-theme="dark"] .table-header {
  background: #fafbfc !important;
  border-bottom: 1px solid #e1e4e8 !important;
  color: #24292e !important;
}

.table-title {
  font-weight: 500;
  font-size: 16px;
  color: #24292e !important;
}

.table-actions {
  display: flex;
  gap: 12px;
}

/* 表单样式 */
.form-container {
  background: #fff;
  border-radius: 6px;
  padding: 20px;
}

.form-actions {
  text-align: center;
  margin-top: 24px;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: 8px;
}

.mb-2 {
  margin-bottom: 16px;
}

.mb-3 {
  margin-bottom: 24px;
}

.mt-0 {
  margin-top: 0;
}

.mt-1 {
  margin-top: 8px;
}

.mt-2 {
  margin-top: 16px;
}

.mt-3 {
  margin-top: 24px;
}

.ml-1 {
  margin-left: 8px;
}

.ml-2 {
  margin-left: 16px;
}

.mr-1 {
  margin-right: 8px;
}

.mr-2 {
  margin-right: 16px;
}

/* 状态样式 */
.status-active {
  color: #52c41a;
}

.status-inactive {
  color: #ff4d4f;
}

.status-pending {
  color: #faad14;
}

/* 响应式 */
@media (max-width: 768px) {
  .layout-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s;
  }
  
  .layout-sidebar.show {
    transform: translateX(0);
  }
  
  .layout-main {
    margin-left: 0;
  }
  
  .layout-content {
    padding: 12px;
  }
  
  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .table-actions {
    justify-content: center;
  }
}

/* 动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

/* NProgress样式覆盖 */
#nprogress .bar {
  background: #409eff !important;
  height: 3px !important;
}

#nprogress .peg {
  box-shadow: 0 0 10px #409eff, 0 0 5px #409eff !important;
}

/* Element Plus样式覆盖 */
.el-menu {
  border-right: none;
  background: transparent !important;
}

.el-menu--dark {
  background: transparent !important;
}

.el-menu--dark .el-menu-item {
  color: rgba(255, 255, 255, 0.8);
  margin: 4px 8px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.el-menu--dark .el-menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.el-menu--dark .el-menu-item:hover::before {
  opacity: 1;
}

.el-menu--dark .el-menu-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #fff;
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.el-menu--dark .el-menu-item.is-active {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3)) !important;
  color: #fff;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.el-menu--dark .el-sub-menu__title {
  color: rgba(255, 255, 255, 0.8);
  margin: 4px 8px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.el-menu--dark .el-sub-menu__title:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #fff;
  transform: translateX(4px);
}

/* 全局动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fadeInLeft {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 玻璃态效果 */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* 渐变文字 */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 悬浮效果 */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.shadow-modern {
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
}

.shadow-modern-lg {
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.1);
}

.rounded-modern {
  border-radius: 12px;
}

.rounded-modern-lg {
  border-radius: 16px;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
